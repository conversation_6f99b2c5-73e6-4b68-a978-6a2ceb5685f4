#include <cstdio>
#include <cstdint>
#include <string>
#include <sys/time.h>
#include <cstring>
#include <sys/stat.h>
#include <unistd.h>

// 最小化版本的图像转换示例，展示如何完善imageTransformation调用

// 简单的文件保存函数
bool saveImageToFile(const char* filename, uint8_t* data, int size) {
    FILE* file = fopen(filename, "wb");
    if (!file) {
        printf("无法创建文件: %s\n", filename);
        return false;
    }
    
    size_t written = fwrite(data, 1, size, file);
    fclose(file);
    
    if (written == (size_t)size) {
        printf("文件保存成功: %s, 大小: %d bytes\n", filename, size);
        return true;
    } else {
        printf("文件写入失败: %s\n", filename);
        return false;
    }
}

// 创建目录
bool createDirectory(const char* path) {
    struct stat st = {0};
    if (stat(path, &st) == -1) {
        if (mkdir(path, 0755) == 0) {
            printf("目录创建成功: %s\n", path);
            return true;
        } else {
            printf("目录创建失败: %s\n", path);
            return false;
        }
    }
    return true; // 目录已存在
}

// 辅助函数：保存转换后的图像数据
bool saveConvertedImage(uint8_t* imageData, int dataSize, int width, int height, const std::string& format) {
    // 确保/userdata目录存在
    createDirectory("/userdata");
    
    // 生成带时间戳的文件名
    struct timeval tv;
    gettimeofday(&tv, nullptr);
    long timestamp = tv.tv_sec * 1000 + tv.tv_usec / 1000;
    
    char filename[256];
    snprintf(filename, sizeof(filename), "/userdata/converted_%dx%d_%ld.%s", 
             width, height, timestamp, format.c_str());
    
    return saveImageToFile(filename, imageData, dataSize);
}

// 创建测试YUV图案
void createTestYUVPattern(uint8_t* yuvBuffer, int width, int height) {
    int ySize = width * height;
    int uvSize = ySize / 2;
    
    printf("创建测试YUV图案: %dx%d, Y大小: %d, UV大小: %d\n", width, height, ySize, uvSize);
    
    // Y分量：创建渐变图案
    for (int y = 0; y < height; y++) {
        for (int x = 0; x < width; x++) {
            int index = y * width + x;
            // 创建水平渐变
            yuvBuffer[index] = (uint8_t)((x * 255) / width);
        }
    }
    
    // UV分量：设置为中性值（128）
    memset(yuvBuffer + ySize, 128, uvSize);
    
    printf("YUV测试图案创建完成\n");
}

// 完善的图像转换函数示例
int demonstrateImageTransformation() {
    printf("=== 完善的图像转换示例 ===\n");
    
    // 原始调用参数
    int srcWidth = 1280, srcHeight = 720;
    int dstWidth = 1280, dstHeight = 720;
    
    // 格式常量（来自RGA头文件）
    const int RK_FORMAT_YCbCr_420_SP = 0x12;  // 18
    const int RK_FORMAT_RGBA_8888 = 0x0;      // 0
    
    printf("原始调用: imageTransformation(1280, 720, 0, RK_FORMAT_YCbCr_420_SP, 1280, 720, 0, RK_FORMAT_RGBA_8888)\n");
    printf("问题: 缺少源图像缓冲区和目标图像缓冲区参数\n\n");
    
    // 计算缓冲区大小
    int srcDataSize = srcWidth * srcHeight * 3 / 2;  // YUV420SP格式
    int dstDataSize = dstWidth * dstHeight * 4;      // RGBA8888格式
    
    printf("源图像缓冲区大小: %d bytes\n", srcDataSize);
    printf("目标图像缓冲区大小: %d bytes\n", dstDataSize);
    
    // 分配缓冲区
    uint8_t* srcBuffer = new uint8_t[srcDataSize];
    uint8_t* dstBuffer = new uint8_t[dstDataSize];
    
    if (!srcBuffer || !dstBuffer) {
        printf("内存分配失败\n");
        delete[] srcBuffer;
        delete[] dstBuffer;
        return -1;
    }
    
    // 创建测试图案
    createTestYUVPattern(srcBuffer, srcWidth, srcHeight);
    
    // 模拟图像转换（实际应该调用RGA硬件）
    printf("\n正在模拟图像转换...\n");
    printf("完善后的调用应该是:\n");
    printf("XuRGAUtils rgaUtils;\n");
    printf("int result = rgaUtils.imageTransformation(\n");
    printf("    %d, %d, XuRGAUtils::IMG_TYPE_NV21, srcBuffer,    // 源图像\n", srcWidth, srcHeight);
    printf("    %d, %d, XuRGAUtils::IMG_TYPE_RGBA8888, dstBuffer // 目标图像\n", dstWidth, dstHeight);
    printf(");\n\n");
    
    // 简化的YUV到RGBA转换模拟
    for (int i = 0; i < dstWidth * dstHeight; i++) {
        int yIndex = i;
        if (yIndex < srcWidth * srcHeight) {
            uint8_t y = srcBuffer[yIndex];
            // 简化转换：Y值直接映射到RGB，Alpha设为255
            dstBuffer[i * 4 + 0] = y;     // R
            dstBuffer[i * 4 + 1] = y;     // G
            dstBuffer[i * 4 + 2] = y;     // B
            dstBuffer[i * 4 + 3] = 255;   // A
        }
    }
    
    printf("图像转换模拟完成！\n");
    
    // 保存转换后的图像
    if (saveConvertedImage(dstBuffer, dstDataSize, dstWidth, dstHeight, "rgba")) {
        printf("图像已成功保存到 /userdata/ 目录\n");
    }
    
    // 释放内存
    delete[] srcBuffer;
    delete[] dstBuffer;
    
    return dstDataSize;
}

int main() {
    printf("图像转换完善示例程序\n");
    printf("展示如何正确使用 XuRGAUtils::imageTransformation 函数\n\n");
    
    // 演示完善的图像转换
    int result = demonstrateImageTransformation();
    
    if (result > 0) {
        printf("\n=== 总结 ===\n");
        printf("原始调用的问题:\n");
        printf("1. 缺少源图像数据缓冲区 (srcBuf)\n");
        printf("2. 缺少目标图像数据缓冲区 (dstBuf)\n");
        printf("3. 没有处理转换结果\n");
        printf("4. 没有保存转换后的图像\n\n");
        
        printf("完善后的要点:\n");
        printf("1. 分配适当大小的源图像和目标图像缓冲区\n");
        printf("2. 填充源图像缓冲区with实际图像数据\n");
        printf("3. 调用imageTransformation函数进行转换\n");
        printf("4. 检查转换结果并保存到/userdata/目录\n");
        printf("5. 释放分配的内存\n\n");
        
        printf("完整的代码示例:\n");
        printf("```cpp\n");
        printf("#include \"utils/XuRGAUtils.h\"\n");
        printf("#include \"utils/XuFile.h\"\n\n");
        printf("// 创建RGA工具实例\n");
        printf("XuRGAUtils rgaUtils;\n\n");
        printf("// 分配缓冲区\n");
        printf("int srcSize = 1280 * 720 * 3 / 2;  // YUV420SP\n");
        printf("int dstSize = 1280 * 720 * 4;      // RGBA8888\n");
        printf("uint8_t* srcBuffer = new uint8_t[srcSize];\n");
        printf("uint8_t* dstBuffer = new uint8_t[dstSize];\n\n");
        printf("// 填充源图像数据 (从摄像头或文件获取)\n");
        printf("// ... fill srcBuffer with actual image data ...\n\n");
        printf("// 执行图像转换\n");
        printf("int result = rgaUtils.imageTransformation(\n");
        printf("    1280, 720, XuRGAUtils::IMG_TYPE_NV21, srcBuffer,\n");
        printf("    1280, 720, XuRGAUtils::IMG_TYPE_RGBA8888, dstBuffer\n");
        printf(");\n\n");
        printf("if (result > 0) {\n");
        printf("    // 保存转换后的图像到/userdata/目录\n");
        printf("    struct timeval tv;\n");
        printf("    gettimeofday(&tv, nullptr);\n");
        printf("    long timestamp = tv.tv_sec * 1000 + tv.tv_usec / 1000;\n");
        printf("    \n");
        printf("    std::string filename = \"/userdata/converted_\" + \n");
        printf("                          std::to_string(timestamp) + \".rgba\";\n");
        printf("    \n");
        printf("    int saveRet = XuFile::getInstance().writeFile(\n");
        printf("        filename.c_str(), dstBuffer, result);\n");
        printf("    \n");
        printf("    if (saveRet > 0) {\n");
        printf("        printf(\"图像转换并保存成功: %%s\\n\", filename.c_str());\n");
        printf("    }\n");
        printf("}\n\n");
        printf("// 释放内存\n");
        printf("delete[] srcBuffer;\n");
        printf("delete[] dstBuffer;\n");
        printf("```\n\n");
        
        printf("转换示例已完成！请检查 /userdata/ 目录中的结果文件。\n");
    } else {
        printf("转换示例执行失败\n");
    }
    
    return 0;
}

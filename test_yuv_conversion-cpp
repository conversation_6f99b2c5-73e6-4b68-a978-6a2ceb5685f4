#include <iostream>
#include <cstdint>
#include <cstring>
#include <fstream>

// 软件实现的YUV420SP到RGBA8888格式转换
int convertYUV420SPToRGBA8888(int srcWidth, int srcHeight, const uint8_t* srcBuf,
                              int dstWidth, int dstHeight, uint8_t* dstBuf) {
    if (!srcBuf || !dstBuf) {
        printf("convertYUV420SPToRGBA8888: 无效的缓冲区指针\n");
        return -1;
    }
    
    if (srcWidth != dstWidth || srcHeight != dstHeight) {
        printf("convertYUV420SPToRGBA8888: 暂不支持缩放，源尺寸和目标尺寸必须相同\n");
        return -1;
    }
    
    printf("开始软件YUV420SP->RGBA8888转换: %dx%d\n", srcWidth, srcHeight);
    
    const uint8_t* yPlane = srcBuf;                           // Y平面
    const uint8_t* uvPlane = srcBuf + srcWidth * srcHeight;   // UV平面 (NV21格式)
    
    // YUV420SP到RGBA8888的转换
    for (int y = 0; y < srcHeight; y++) {
        for (int x = 0; x < srcWidth; x++) {
            // 获取Y值
            int yIndex = y * srcWidth + x;
            int Y = yPlane[yIndex];
            
            // 获取U和V值 (NV21格式: VUVUVU...)
            int uvIndex = (y / 2) * srcWidth + (x / 2) * 2;
            int V = uvPlane[uvIndex];     // V在前
            int U = uvPlane[uvIndex + 1]; // U在后
            
            // YUV到RGB转换公式
            int R = Y + 1.402 * (V - 128);
            int G = Y - 0.344 * (U - 128) - 0.714 * (V - 128);
            int B = Y + 1.772 * (U - 128);
            
            // 限制RGB值在0-255范围内
            R = (R < 0) ? 0 : (R > 255) ? 255 : R;
            G = (G < 0) ? 0 : (G > 255) ? 255 : G;
            B = (B < 0) ? 0 : (B > 255) ? 255 : B;
            
            // 写入RGBA8888格式 (R, G, B, A)
            int rgbaIndex = (y * dstWidth + x) * 4;
            dstBuf[rgbaIndex + 0] = (uint8_t)R;     // R
            dstBuf[rgbaIndex + 1] = (uint8_t)G;     // G
            dstBuf[rgbaIndex + 2] = (uint8_t)B;     // B
            dstBuf[rgbaIndex + 3] = 255;            // A (不透明)
        }
    }
    
    int outputSize = dstWidth * dstHeight * 4;
    printf("软件转换完成，输出大小: %d bytes\n", outputSize);
    return outputSize;
}

// 创建测试用的YUV420SP数据
void createTestYUVData(uint8_t* buffer, int width, int height) {
    int ySize = width * height;
    int uvSize = ySize / 2;
    
    // 填充Y平面 - 创建一个渐变图案
    for (int y = 0; y < height; y++) {
        for (int x = 0; x < width; x++) {
            int index = y * width + x;
            // 创建从左到右的亮度渐变
            buffer[index] = (uint8_t)(x * 255 / width);
        }
    }
    
    // 填充UV平面 - 创建颜色变化
    uint8_t* uvPlane = buffer + ySize;
    for (int y = 0; y < height / 2; y++) {
        for (int x = 0; x < width / 2; x++) {
            int index = y * width + x * 2;
            // V分量 - 从上到下变化
            uvPlane[index] = (uint8_t)(y * 255 / (height / 2));
            // U分量 - 固定值
            uvPlane[index + 1] = 128;
        }
    }
}

// 保存RGBA数据为PPM文件（用于验证）
bool saveRGBAasPPM(const uint8_t* rgbaData, int width, int height, const char* filename) {
    std::ofstream file(filename, std::ios::binary);
    if (!file.is_open()) {
        printf("无法创建文件: %s\n", filename);
        return false;
    }
    
    // PPM文件头
    file << "P6\n" << width << " " << height << "\n255\n";
    
    // 写入RGB数据（跳过Alpha通道）
    for (int i = 0; i < width * height; i++) {
        file.write((char*)&rgbaData[i * 4], 3); // 只写RGB，跳过A
    }
    
    file.close();
    printf("RGBA数据已保存为PPM文件: %s\n", filename);
    return true;
}

int main() {
    printf("=== YUV420SP到RGBA8888转换测试 ===\n");
    
    // 测试参数
    int width = 320;
    int height = 240;
    int yuvSize = width * height * 3 / 2;  // YUV420SP格式
    int rgbaSize = width * height * 4;     // RGBA8888格式
    
    // 分配缓冲区
    uint8_t* yuvBuffer = new uint8_t[yuvSize];
    uint8_t* rgbaBuffer = new uint8_t[rgbaSize];
    
    // 创建测试数据
    printf("创建测试YUV数据: %dx%d\n", width, height);
    createTestYUVData(yuvBuffer, width, height);
    
    // 执行转换
    int result = convertYUV420SPToRGBA8888(width, height, yuvBuffer, width, height, rgbaBuffer);
    
    if (result > 0) {
        printf("转换成功！输出大小: %d bytes\n", result);
        
        // 保存结果为PPM文件用于验证
        saveRGBAasPPM(rgbaBuffer, width, height, "test_output.ppm");
        
        // 显示一些像素值用于验证
        printf("\n前几个像素的RGBA值:\n");
        for (int i = 0; i < 5; i++) {
            int idx = i * 4;
            printf("像素%d: R=%d, G=%d, B=%d, A=%d\n", i, 
                   rgbaBuffer[idx], rgbaBuffer[idx+1], rgbaBuffer[idx+2], rgbaBuffer[idx+3]);
        }
        
    } else {
        printf("转换失败，错误码: %d\n", result);
    }
    
    // 释放内存
    delete[] yuvBuffer;
    delete[] rgbaBuffer;
    
    printf("=== 测试完成 ===\n");
    return 0;
}

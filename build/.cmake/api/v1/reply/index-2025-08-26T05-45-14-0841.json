{"cmake": {"generator": {"multiConfig": false, "name": "Unix Makefiles"}, "paths": {"cmake": "/usr/bin/cmake", "cpack": "/usr/bin/cpack", "ctest": "/usr/bin/ctest", "root": "/usr/share/cmake-3.28"}, "version": {"isDirty": false, "major": 3, "minor": 28, "patch": 3, "string": "3.28.3", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-d67b6cc2b9935e38e270.json", "kind": "codemodel", "version": {"major": 2, "minor": 6}}, {"jsonFile": "cache-v2-543eafab4c5fe194adc1.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-6b5fbde8f071c547d1db.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-integration-vscode": {"cache-v2": {"jsonFile": "cache-v2-543eafab4c5fe194adc1.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-6b5fbde8f071c547d1db.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-d67b6cc2b9935e38e270.json", "kind": "codemodel", "version": {"major": 2, "minor": 6}}}}}
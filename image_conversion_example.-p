#include <iostream>
#include <cstdint>
#include <cstring>
#include <fstream>
#include <sys/time.h>
#include <sys/stat.h>
#include <unistd.h>
#include "utils/XuRGAUtils.h"

// 辅助函数：创建目录
bool createDirectory(const std::string& path) {
    struct stat st = {0};
    if (stat(path.c_str(), &st) == -1) {
        if (mkdir(path.c_str(), 0755) == -1) {
            printf("无法创建目录: %s\n", path.c_str());
            return false;
        }
    }
    return true;
}

// 辅助函数：保存转换后的图像数据
bool saveConvertedImage(uint8_t* imageData, int dataSize, int width, int height, const std::string& format) {
    // 确保/userdata目录存在
    if (!createDirectory("/userdata")) {
        return false;
    }

    // 生成带时间戳的文件名
    struct timeval tv;
    gettimeofday(&tv, nullptr);
    long timestamp = tv.tv_sec * 1000 + tv.tv_usec / 1000;

    std::string filename = "/userdata/converted_" + std::to_string(width) + "x" + std::to_string(height) +
                          "_" + std::to_string(timestamp) + "." + format;

    std::ofstream file(filename, std::ios::binary);
    if (!file.is_open()) {
        printf("图像保存失败: %s\n", filename.c_str());
        return false;
    }

    file.write((char*)imageData, dataSize);
    file.close();

    if (file.good()) {
        printf("图像转换并保存成功: %s, 尺寸: %dx%d, 大小: %d bytes\n",
               filename.c_str(), width, height, dataSize);
        return true;
    } else {
        printf("图像保存失败: %s\n", filename.c_str());
        return false;
    }
}

// 创建测试用的YUV420SP数据
void createTestYUVData(uint8_t* buffer, int width, int height) {
    int ySize = width * height;
    int uvSize = ySize / 2;
    
    // 填充Y平面 - 创建一个渐变图案
    for (int y = 0; y < height; y++) {
        for (int x = 0; x < width; x++) {
            int index = y * width + x;
            // 创建从左到右的亮度渐变
            buffer[index] = (uint8_t)(x * 255 / width);
        }
    }
    
    // 填充UV平面 - 创建颜色变化
    uint8_t* uvPlane = buffer + ySize;
    for (int y = 0; y < height / 2; y++) {
        for (int x = 0; x < width / 2; x++) {
            int index = y * width + x * 2;
            // V分量 - 从上到下变化
            uvPlane[index] = (uint8_t)(y * 255 / (height / 2));
            // U分量 - 固定值
            uvPlane[index + 1] = 128;
        }
    }
}

// 创建简单的测试图案
void createTestPattern(uint8_t* buffer, int width, int height) {
    int ySize = width * height;
    
    // 填充Y平面 - 创建棋盘图案
    for (int y = 0; y < height; y++) {
        for (int x = 0; x < width; x++) {
            int index = y * width + x;
            // 创建棋盘图案
            if (((x / 64) + (y / 64)) % 2 == 0) {
                buffer[index] = 200; // 亮色
            } else {
                buffer[index] = 50;  // 暗色
            }
        }
    }
    
    // 填充UV平面
    uint8_t* uvPlane = buffer + ySize;
    for (int i = 0; i < ySize / 2; i++) {
        uvPlane[i] = 128; // 中性色度
    }
}

// 执行图像转换的函数
int performImageTransformation(int srcWidth, int srcHeight, int srcFormat,
                             int dstWidth, int dstHeight, int dstFormat) {
    printf("=== 开始图像转换 ===\n");
    printf("源图像: %dx%d, 格式: %d\n", srcWidth, srcHeight, srcFormat);
    printf("目标图像: %dx%d, 格式: %d\n", dstWidth, dstHeight, dstFormat);

    // 创建XuRGAUtils实例
    XuRGAUtils rgaUtils;

    // 计算缓冲区大小
    int srcDataSize, dstDataSize;
    
    // 根据格式计算源图像数据大小
    switch (srcFormat) {
        case XuRGAUtils::IMG_TYPE_NV21:
        case XuRGAUtils::IMG_TYPE_NV12:
            srcDataSize = srcWidth * srcHeight * 3 / 2;
            break;
        case XuRGAUtils::IMG_TYPE_RGB888:
        case XuRGAUtils::IMG_TYPE_BGR888:
            srcDataSize = srcWidth * srcHeight * 3;
            break;
        case XuRGAUtils::IMG_TYPE_RGBA8888:
        case XuRGAUtils::IMG_TYPE_BGRA8888:
            srcDataSize = srcWidth * srcHeight * 4;
            break;
        default:
            printf("不支持的源图像格式: %d\n", srcFormat);
            return -1;
    }

    // 根据格式计算目标图像数据大小
    switch (dstFormat) {
        case XuRGAUtils::IMG_TYPE_NV21:
        case XuRGAUtils::IMG_TYPE_NV12:
            dstDataSize = dstWidth * dstHeight * 3 / 2;
            break;
        case XuRGAUtils::IMG_TYPE_RGB888:
        case XuRGAUtils::IMG_TYPE_BGR888:
            dstDataSize = dstWidth * dstHeight * 3;
            break;
        case XuRGAUtils::IMG_TYPE_RGBA8888:
        case XuRGAUtils::IMG_TYPE_BGRA8888:
            dstDataSize = dstWidth * dstHeight * 4;
            break;
        default:
            printf("不支持的目标图像格式: %d\n", dstFormat);
            return -1;
    }

    // 分配缓冲区
    uint8_t* srcBuffer = new uint8_t[srcDataSize];
    uint8_t* dstBuffer = new uint8_t[dstDataSize];

    // 创建测试数据
    if (srcFormat == XuRGAUtils::IMG_TYPE_NV21) {
        createTestYUVData(srcBuffer, srcWidth, srcHeight);
        printf("已创建YUV测试图案\n");
    } else {
        createTestPattern(srcBuffer, srcWidth, srcHeight);
        printf("已创建简单测试图案\n");
    }

    printf("正在执行RGA图像转换...\n");

    // 执行图像格式转换
    int convertRet = rgaUtils.imageTransformation(
        srcWidth, srcHeight, srcFormat, srcBuffer,    // 源图像
        dstWidth, dstHeight, dstFormat, dstBuffer     // 目标图像
    );

    if (convertRet > 0) {
        printf("图像转换成功！输出数据大小: %d bytes (预期: %d bytes)\n", convertRet, dstDataSize);

        // 确定文件扩展名
        std::string extension;
        switch (dstFormat) {
            case XuRGAUtils::IMG_TYPE_NV21:
            case XuRGAUtils::IMG_TYPE_NV12:
                extension = "yuv";
                break;
            case XuRGAUtils::IMG_TYPE_RGB888:
                extension = "rgb";
                break;
            case XuRGAUtils::IMG_TYPE_BGR888:
                extension = "bgr";
                break;
            case XuRGAUtils::IMG_TYPE_RGBA8888:
                extension = "rgba";
                break;
            case XuRGAUtils::IMG_TYPE_BGRA8888:
                extension = "bgra";
                break;
            default:
                extension = "raw";
                break;
        }

        // 保存转换后的图像
        if (saveConvertedImage(dstBuffer, convertRet, dstWidth, dstHeight, extension)) {
            printf("图像已成功保存到 /userdata/ 目录\n");
        }
    } else {
        printf("图像转换失败，错误码: %d\n", convertRet);
    }

    // 释放内存
    delete[] srcBuffer;
    delete[] dstBuffer;

    printf("=== 图像转换完成 ===\n\n");
    return convertRet;
}

int main() {
    printf("图像转换示例程序\n");
    printf("功能：使用XuRGAUtils进行图像格式转换并保存到/userdata/目录\n\n");

    // 示例1: YUV420SP -> RGBA8888
    printf("示例1: YUV420SP -> RGBA8888\n");
    performImageTransformation(1280, 720, XuRGAUtils::IMG_TYPE_NV21, 
                             1280, 720, XuRGAUtils::IMG_TYPE_RGBA8888);

    // 示例2: YUV420SP -> RGB888 (缩放)
    printf("示例2: YUV420SP -> RGB888 (缩放)\n");
    performImageTransformation(1280, 720, XuRGAUtils::IMG_TYPE_NV21, 
                             640, 360, XuRGAUtils::IMG_TYPE_RGB888);

    // 示例3: YUV420SP -> RGBA8888 (缩放到720x576)
    printf("示例3: YUV420SP -> RGBA8888 (缩放到720x576)\n");
    performImageTransformation(1280, 720, XuRGAUtils::IMG_TYPE_NV21, 
                             720, 576, XuRGAUtils::IMG_TYPE_RGBA8888);

    printf("所有转换示例已完成！\n");
    printf("请检查 /userdata/ 目录中的转换结果文件。\n");

    return 0;
}

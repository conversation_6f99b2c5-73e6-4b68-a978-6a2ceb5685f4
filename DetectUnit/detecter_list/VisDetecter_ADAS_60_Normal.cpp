//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/10/30.
//

#include "VisDetecter_ADAS_60_Normal.h"
#include "dasDetect.h"
#include "XuTimeUtil.h"
#include "XuString.h"
#include "XuShell.h"

namespace vis {

    using namespace cv;
    using namespace std;

    VisDetecter_ADAS_60_Normal::VisDetecter_ADAS_60_Normal() {
        curAlgorithmType = ALGORITHM_TYPE_ADAS_60_NORMAL;
    }

    void VisDetecter_ADAS_60_Normal::run() {
        std::string pthreadName = "ADAS_60_";
        pthreadName.append(std::to_string(curCameraId));
        pthread_setname_np(pthread_self(), pthreadName.c_str());

        doDetecter();
        pthread_setname_np(pthread_self(), "Finish");
    }

    void VisDetecter_ADAS_60_Normal::doDetecter() {
        try{
            int ret = -1;



            /* 需要判断一下是否开启限速标示的算法，有开了限速标示的报警决策才需要打开算法，没有就不用 */
            bool openTSR = alarmDecisionList.alarmDecisionTsrAdas != nullptr;

            das::Detect *detect = new das::Detect();
            detect->setAlgMode(YOLOV5DS_ADAS_60_MODE);  //设置算法模式，很重要！！！不然容易出错
            detect->setIsRunTSR(openTSR); //设置一下是否启动TSR，仅对YOLOV5DS_ADAS_60_MODE有效
            const int input_width = 1280;
            const int input_height = 720;

            das::objectInfo_t object_info;

            object_info.objects.clear();

            Mat srcimg = Mat::zeros(input_height, input_width, CV_8UC3);

            std::vector<cv::Point> ignoreArea1;
            std::vector<cv::Point> ignoreArea2;
            ignoreArea1 = getIgnoreAreaPoint1();
            ignoreArea2 = getIgnoreAreaPoint2();
            detect->init(input_width, input_height,ignoreArea1,ignoreArea2);
            detectOpen = true;
            printf("=================================================ADAS_60_Normal init finish!  version=%s \n",detect->getModelVersion());
            /* 把算法版本号存起来 */
            switch (curCameraId) {
                case CAMERA_ID_1:{
                    setenv("G3_CAM1_ALG_VERSION",detect->getModelVersion(), 1);
                }
                    break;
                case CAMERA_ID_2:{
                    setenv("G3_CAM2_ALG_VERSION",detect->getModelVersion(), 1);
                }
                    break;
                case CAMERA_ID_3:{
                    setenv("G3_CAM3_ALG_VERSION",detect->getModelVersion(), 1);
                }
                    break;
                case CAMERA_ID_4:{
                    setenv("G3_CAM4_ALG_VERSION",detect->getModelVersion(), 1);
                }
                    break;
            }

            uint8_t *cameraYuvData = (uint8_t *)malloc(input_width * input_height * 3 / 2);
            Mat yuvImg = Mat::zeros(input_height * 3 / 2, input_width, CV_8UC1);

            int frame_cnt = 0;
            bool isCamCoverd = false;
//            bool isFullBlack = false;
            float imgBrightness = -1;

            /* 镜头遮挡状态的持续累加计数 */
            int camcoverd_cnt = 0;
            /* 镜头全黑状态的持续累加计数 */
            int fullblack_cnt = 0;
            /* 是否需要清理识别信息 */
            bool needclean = false;
            while (1) {
                frame_cnt++;

//                double BeginTime_alg = (double) cv::getTickCount();

                ret = curDetectDataCallback->getYUVData(curCameraId, cameraYuvData,curVehicleStatus,&needclean);

                if(!needclean){
                    if (ret != 0) {
                        usleep(5 * 1000);
                        continue;
                    }
//            printf("get yuv data success, cameraId=%d size=%d  \n", cameraYuvData.getCameraId(),cameraYuvData.getDataLen());

                    xuRgaUtils.imageTransformation(1280, 720, xuRgaUtils.IMG_TYPE_NV21, cameraYuvData, 1280,
                                                   720,
                                                   xuRgaUtils.IMG_TYPE_BGR888, srcimg.data);
//            yuvImg.data = cameraYuvData.getCurYuvData();
//            cvtColor(yuvImg, srcimg, COLOR_YUV2BGR_NV12);




                    // 设置一下报警开关
                    detect->setAlarmOnOff((G3_Configuration::getInstance().getAdasPdwSwitch() == 1),
                                          (G3_Configuration::getInstance().getAdasFcwSwitch() == 1),
                                          (G3_Configuration::getInstance().getAdasLdwSwitch() == 1),
                                          (G3_Configuration::getInstance().getAdasVbSwitch() == 1),
                                          (G3_Configuration::getInstance().getAdasGoSwitch() == 1));

//            srcimg = imread("/userdata/xryimg.jpg");
                    uint64_t beforeDetectTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis();
                    detect->detect(srcimg, curVehicleStatus.speed/(float)3.6);
                    curDetectUseTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis() - beforeDetectTime;
                    object_info.objects.clear();
                    object_info.traffics.clear();
                    object_info.lanes.clear();

                    /* 设置一下是否需要车道线标定 */
                    detect->getAlarm()->setIsRunCalibrationFineTuning(G3_Configuration::getInstance().getAdasLaneIntersectionCalc() == 1);//当客户端手动标定完成后，220会调用isRunCalibrationFineTuning的set函数把isRunCalibrationFineTuning设为true
                    /* 设置一下车道线报警是否需要虚线报警 */
                    detect->setisDashAlarm((G3_Configuration::getInstance().getAdasLdwDashAlarmSwitch() == 1));



                    object_info = detect->getResult();
                    detect->executeAlarmStrategy(object_info, curVehicleStatus);

                    printf("adas 60 m_ldw_left_solid_alarm=%d  m_ldw_left_dash_alarm=%d \n",object_info.alarmInfo.m_ldw_left_solid_alarm,object_info.alarmInfo.m_ldw_left_dash_alarm);


                    if (frame_cnt % 50 == 3){


                        /* 先检测下当前帧是否遮挡或者全黑，并拿出亮度 */
                        bool curIsCamCoverd = false;
                        bool curIsFullBlack = false;
                        das::CalcCamCoverd(srcimg, imgBrightness, curIsCamCoverd, curIsFullBlack);  //rv1126上约20-30ms，暂定G3取需要的isCamCoverd及imgBrightness直接从这个函数中取，目前没有从objinfo中取
                        /* 计算下连续总的遮挡的次数 */
                        if (curIsCamCoverd){
                            camcoverd_cnt++;
                        }else{
                            camcoverd_cnt = 0;
                        }
                        /* 计算下连续总的全黑的次数 */
                        if (curIsFullBlack){
                            fullblack_cnt++;
                        }else{
                            fullblack_cnt = 0;
                        }
                        /* 如果连续总的遮挡次数达到了阈值，那么就是被遮挡了 */
                        isCamCoverd = (camcoverd_cnt >= G3_Configuration::getInstance().getBsdCamcoverCountThreshold());
//                    /* 如果连续总的全黑次数达到了阈值，那么就是全黑了 */
//                    isFullBlack = (fullblack_cnt > G3_Configuration::getInstance().getBsdCamcoverCountThreshold());



                    }
                    object_info.alarmInfo.m_camera_covered_alarm = isCamCoverd;

                    /* 看看是不是标定成功了 */
                    if (detect->getAlarm()->getIsInitCalled() == true) //如果自动标定成功isInitCalled会被置为true
                    {
                        /* 设置一下算法的标定变量，让算法不用标定了 */
                        detect->getAlarm()->setIsInitCalled(false);  //220拿完新数据把isInitCalled置为false
                        detect->getAlarm()->setIsRunCalibrationFineTuning(false); //220拿完新数据把isRunCalibrationFineTuning置为false
                        /* 拿完新数据了，那么需要保存到配置表 */
                        das::CamOutMRV220 newValues = detect->getParam_()->getNewValue();
                        G3_Configuration::getInstance().setAdasHorizonY(newValues.horizontal_line);
                        G3_Configuration::getInstance().setAdasVerticalLine(newValues.vertical_line);
                        // 根据中心点计算外参并保存到配置表
                        G3_Configuration::getInstance().setAdasM00(newValues.m0_0);
                        G3_Configuration::getInstance().setAdasM01(newValues.m0_1);
                        G3_Configuration::getInstance().setAdasM02(newValues.m0_2);
                        G3_Configuration::getInstance().setAdasM03(newValues.m0_3);
                        G3_Configuration::getInstance().setAdasM10(newValues.m1_0);
                        G3_Configuration::getInstance().setAdasM11(newValues.m1_1);
                        G3_Configuration::getInstance().setAdasM12(newValues.m1_2);
                        G3_Configuration::getInstance().setAdasM13(newValues.m1_3);
                        G3_Configuration::getInstance().setAdasM20(newValues.m2_0);
                        G3_Configuration::getInstance().setAdasM21(newValues.m2_1);
                        G3_Configuration::getInstance().setAdasM22(newValues.m2_2);
                        G3_Configuration::getInstance().setAdasM23(newValues.m2_3);
                        // 设置不用校准的标识符号
                        G3_Configuration::getInstance().setAdasLaneIntersectionCalc(0);
                        // 保存下最新的配置表文件
                        G3_Configuration::getInstance().saveToFile();
                    }

                    /* 这里交给报警决策判断一下 */
                    toAlarmDecision(object_info,curDetectUseTime);

                    lastDetectTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis();
                }else{
                    /* 需要清理掉旧的识别信息，那么就发个空的object去给报警决策 */
                    object_info.objects.clear();
                    object_info.lanes.clear();
                    object_info.traffics.clear();
                    object_info.alarmInfo = {};
                    object_info.faultInfo = {};
                    curDetectUseTime = 40;
                    toAlarmDecision(object_info, curDetectUseTime);
                    /* 记录下这次完成识别的时间 */
                    lastDetectTime = XuTimeUtil::getInstance().getTimeFromSysStartUp_Millis();
                    usleep(curDetectUseTime * 1000);
                }




            }


            delete detect;
        }catch (...){
            printf("error=%s \n",strerror(errno));
        }



        return;
    }

    void VisDetecter_ADAS_60_Normal::toAlarmDecision(das::objectInfo_t &object_info,const uint16_t curDetectUseTime) {
        /* 看看需不需要塞到行人(区域)的识别的报警决策里 */
        if(alarmDecisionList.alarmDecisionPedestrianArea != nullptr && alarmDecisionList.alarmDecisionPedestrianArea->isInited()){
            alarmDecisionList.alarmDecisionPedestrianArea->parse_SBST_160_SEMANTIC_SEGMENTATION(object_info,curVehicleStatus,curDetectUseTime);
        }

        /* 看看需不需要塞到车辆（区域）的识别的报警决策里 */
        if(alarmDecisionList.alarmDecisionVehicleArea != nullptr && alarmDecisionList.alarmDecisionVehicleArea->isInited()){
            alarmDecisionList.alarmDecisionVehicleArea->parse_SBST_160_SEMANTIC_SEGMENTATION(object_info,curVehicleStatus,curDetectUseTime);
        }

        /* 看看需不需要塞到行人(TTC)的识别的报警决策里 */
        if(alarmDecisionList.alarmDecisionPedestrianStandard != nullptr && alarmDecisionList.alarmDecisionPedestrianStandard->isInited()){
            alarmDecisionList.alarmDecisionPedestrianStandard->parse_SBST_160_SEMANTIC_SEGMENTATION(object_info,curVehicleStatus,curDetectUseTime);
        }
        /* 看看需不需要塞到车辆(TTC)的识别的报警决策里 */
        if(alarmDecisionList.alarmDecisionVehicleStandard != nullptr && alarmDecisionList.alarmDecisionVehicleStandard->isInited()){
            alarmDecisionList.alarmDecisionVehicleStandard->parse_SBST_160_SEMANTIC_SEGMENTATION(object_info,curVehicleStatus,curDetectUseTime);
        }

        /* 看看需不需要塞到车道线(TTC)的识别的报警决策里 */
        if(alarmDecisionList.alarmDecisionLanelineAdas != nullptr && alarmDecisionList.alarmDecisionLanelineAdas->isInited()){
            alarmDecisionList.alarmDecisionLanelineAdas->parse_SBST_160_SEMANTIC_SEGMENTATION(object_info,curVehicleStatus,curDetectUseTime);
        }

        /* 看看需不需要塞到行人（SBST后向）的识别的报警决策里 */
        if(alarmDecisionList.alarmDecisionPedestrianSbstBackward != nullptr && alarmDecisionList.alarmDecisionPedestrianSbstBackward->isInited()){
            alarmDecisionList.alarmDecisionPedestrianSbstBackward->parse_SBST_160_SEMANTIC_SEGMENTATION(object_info,curVehicleStatus,curDetectUseTime);
        }

        /* 看看需不需要塞到车辆（SBST后向）的识别的报警决策里 */
        if(alarmDecisionList.alarmDecisionVehicleSbstBackward != nullptr && alarmDecisionList.alarmDecisionVehicleSbstBackward->isInited()){
            alarmDecisionList.alarmDecisionVehicleSbstBackward->parse_SBST_160_SEMANTIC_SEGMENTATION(object_info,curVehicleStatus,curDetectUseTime);
        }

        /* 看看需不需要塞到ADAS的限速标示的报警决策里 */
        if(alarmDecisionList.alarmDecisionTsrAdas != nullptr && alarmDecisionList.alarmDecisionTsrAdas->isInited()){
            alarmDecisionList.alarmDecisionTsrAdas->parse_SBST_160_SEMANTIC_SEGMENTATION(object_info,curVehicleStatus,curDetectUseTime);
        }

        /* 看看需不需要塞到行人（R159）的报警决策里 */
        if(alarmDecisionList.alarmDecisionPedestrianR159 != nullptr && alarmDecisionList.alarmDecisionPedestrianR159->isInited()){
            alarmDecisionList.alarmDecisionPedestrianR159->parse_SBST_160_SEMANTIC_SEGMENTATION(object_info,curVehicleStatus,curDetectUseTime);
        }
    }

    bool
    VisDetecter_ADAS_60_Normal::CalculateVanishingPoint(std::vector<das::lane_t> curves, Point2i &vanishing_point) {
        bool is_calculate_success = true;

        bool has_left_lane = false;
        bool has_right_lane = false;

        double left_A0, left_A1, left_A2, left_A3;
        double right_A0, right_A1, right_A2, right_A3;

        int left_AX, left_AY, left_BX, left_BY;
        int right_AX, right_AY, right_BX, right_BY;

        for (std::size_t i = 0; i < curves.size(); i++)
        {
            double len = sqrt((curves[i].mEndpointAY - curves[i].mEndpointBY) * (curves[i].mEndpointAY - curves[i].mEndpointBY) + (curves[i].mEndpointAX - curves[i].mEndpointBX) * (curves[i].mEndpointAX - curves[i].mEndpointBX));

            const double len_th = 250.0;

            if ((curves[i].mIsLeftLane) && (len > len_th))
            {
                left_A0 = curves[i].mA0;
                left_A1 = curves[i].mA1;
                left_A2 = curves[i].mA2;
                left_A3 = curves[i].mA3;
                left_AX = curves[i].mEndpointAX;
                left_AY = curves[i].mEndpointAY;
                left_BX = curves[i].mEndpointBX;
                left_BY = curves[i].mEndpointBY;
                has_left_lane = true;
            }
            if ((curves[i].mIsRightLane) && (len > len_th))
            {
                right_A0 = curves[i].mA0;
                right_A1 = curves[i].mA1;
                right_A2 = curves[i].mA2;
                right_A3 = curves[i].mA3;
                right_AX = curves[i].mEndpointAX;
                right_AY = curves[i].mEndpointAY;
                right_BX = curves[i].mEndpointBX;
                right_BY = curves[i].mEndpointBY;
                has_right_lane = true;
            }
        }

        if (has_left_lane == true && has_right_lane == true)
        {
            float left_k, left_b, right_k, right_b;
            LineFitting(left_k, left_b, left_A0, left_A1, left_A2, left_A3, left_AX, left_AY, left_BX, left_BY);
            LineFitting(right_k, right_b, right_A0, right_A1, right_A2, right_A3, right_AX, right_AY, right_BX, right_BY);

            if (left_k * right_k < 0.0)
            {
                bool has_vanishing_point = false;
                for (int y = 0; y < 720; y++)
                {
                    float left_x = left_b + left_k * y;
                    float right_x = right_b + right_k * y;
                    if (fabs(left_x - right_x) <= 1)
                    {
                        vanishing_point.x = (int)left_x;
                        vanishing_point.y = y;

                        has_vanishing_point = true;
                        is_calculate_success = true;

                        break;
                    }
                }

                if (has_vanishing_point == false)
                    is_calculate_success = false;
            }
            else
            {
                is_calculate_success = false;
            }

            /*
            bool has_vanishing_point = false;
            for (int y = 0; y < 720; y++)
            {
                double left_x = left_A0 + left_A1 * y + left_A2 * std::pow(y, 2) + left_A3 * std::pow(y, 3);
                double right_x = right_A0 + right_A1 * y + right_A2 * std::pow(y, 2) + right_A3 * std::pow(y, 3);
                if (fabs(left_x - right_x) <= 1)
                {
                    vanishing_point.x = (int)left_x;
                    vanishing_point.y = y;

                    has_vanishing_point = true;
                    is_calculate_success = true;

                    break;
                }
            }

            if (has_vanishing_point == false)
                is_calculate_success = false;
            */
        }
        else
        {
            is_calculate_success = false;
        }

        return is_calculate_success;
    }

    void VisDetecter_ADAS_60_Normal::newout(Point2i &point) {


        float camera_f = 6;
        float x_bian;
        float y_bian;
        float tanx;
        float tany;
        float du_x;
        float du_y;
        float x_b;
        float y_b;
        float m_cy = 373.4179308;
        float m_cx = 670.1643446;
        float m_fy = 1730.547707;
        float m_fx = 1758.315485;
        y_b = m_cy - point.y;
        x_b = m_cx - point.x;
        y_bian = y_b * camera_f / m_fy;
        x_bian = -x_b * camera_f / m_fx;


        tany = y_bian / camera_f;
        tanx = x_bian / camera_f;




        du_y = std::atan(tany);
        du_x = std::atan(tanx);


        cv::Mat RX = (cv::Mat_<double>(3, 3) << 1, 0, 0, 0, std::cos(du_y), std::sin(du_y), 0, std::sin(du_y), -std::cos(du_y));
        cv::Mat RY = (cv::Mat_<double>(3, 3) << std::cos(du_x), 0, std::sin(du_x), 0, 1, 0, std::sin(du_x), 0, -std::cos(du_x));
        cv::Mat RZ = (cv::Mat_<double>(3, 3) << std::cos(0), std::sin(0), 0, -std::sin(0), std::cos(0), 0, 0, 0, 1);
        cv::Mat R = RX * RY*RZ;

        double m0_0 = R.at<double>(0, 0);
        double m0_1 = R.at<double>(0, 1);
        double m0_2 = R.at<double>(0, 2);
        double m0_3 = 0;
        double m1_0 = R.at<double>(1, 0);
        double m1_1 = R.at<double>(1, 1);
        double m1_2 = R.at<double>(1, 2);
        double m1_3 = 0;
        double m2_0 = R.at<double>(2, 0);
        double m2_1 = R.at<double>(2, 1);
        double m2_2 = R.at<double>(2, 2);
        double m2_3 = 0;

        G3_Configuration::getInstance().setAdasM00(m0_0);
        G3_Configuration::getInstance().setAdasM01(m0_1);
        G3_Configuration::getInstance().setAdasM02(m0_2);
        G3_Configuration::getInstance().setAdasM03(m0_3);
        G3_Configuration::getInstance().setAdasM10(m1_0);
        G3_Configuration::getInstance().setAdasM11(m1_1);
        G3_Configuration::getInstance().setAdasM12(m1_2);
        G3_Configuration::getInstance().setAdasM13(m1_3);
        G3_Configuration::getInstance().setAdasM20(m2_0);
        G3_Configuration::getInstance().setAdasM21(m2_1);
        G3_Configuration::getInstance().setAdasM22(m2_2);
        G3_Configuration::getInstance().setAdasM23(m2_3);

    }

    void bubbleSort(int arr[], int n)
    {
        int i, j, temp;
        for (i = 0; i < n - 1; i++)
        {
            // 最后的i个元素已经排好序，不需要再比较
            for (j = 0; j < n - i - 1; j++)
            {
                if (arr[j] > arr[j + 1])
                {
                    // 交换arr[j]和arr[j+1]
                    temp = arr[j];
                    arr[j] = arr[j + 1];
                    arr[j + 1] = temp;
                }
            }
        }
    }

    bool
    VisDetecter_ADAS_60_Normal::calibration_fine_tuning(Point2i &vanishing_point, std::vector<das::lane_t> curves) {
        //标定设置好十字架后，在干净的道路上跑一段时间，自动对十字架进行微调

        bool isFineTuningSuccess = false;

        if (CalculateVanishingPoint(curves, vanishing_point))
        {
            stat_x[vanishing_point_cnt] = vanishing_point.x;
            stat_y[vanishing_point_cnt] = vanishing_point.y;

            vanishing_point_cnt++;
        }

        //printf("cnt = %d, sum_x = %d, sum_y = %d\n", vanishing_point_cnt, vanishing_point_sum_x, vanishing_point_sum_y);
        if (vanishing_point_cnt == vanishing_point_cnt_th)
        {
            bubbleSort(stat_x, vanishing_point_cnt_th);
            bubbleSort(stat_y, vanishing_point_cnt_th);

            int sum_cnt = 0;
            for (int t = vanishing_point_cnt_th * 0.1; t < vanishing_point_cnt_th * 0.9; t++)
            {
                vanishing_point_sum_x += stat_x[t];
                vanishing_point_sum_y += stat_y[t];
                sum_cnt++;
            }

            vanishing_point.x = (float)vanishing_point_sum_x / sum_cnt;
            vanishing_point.y = (float)vanishing_point_sum_y / sum_cnt;

            //vanishing_point.x = (float)vanishing_point_sum_x / vanishing_point_cnt;
            //vanishing_point.y = (float)vanishing_point_sum_y / vanishing_point_cnt;

            //printf("x = %d, y = %d\n", vanishing_point.x, vanishing_point.y);
            //getchar();

            isFineTuningSuccess = true;
        }

        return isFineTuningSuccess;
    }

    void VisDetecter_ADAS_60_Normal::LineFitting(float &k, float &b, double A0, double A1, double A2, double A3, int AX,
                                                 int AY, int BX, int BY) {
        //使用直线拟合方式

        /*
        std::vector<cv::Point> fitting_points;

        int y1 = MIN(AY, BY);
        int y4 = MAX(AY, BY);
        int y2 = y1 + (y4 - y1) * 0.33;
        int y3 = y1 + (y4 - y1) * 0.67;

        int x1 = A0 + A1 * y1 + A2 * std::pow(y1, 2) + A3 * std::pow(y1, 3);
        int x2 = A0 + A1 * y2 + A2 * std::pow(y2, 2) + A3 * std::pow(y2, 3);
        int x3 = A0 + A1 * y3 + A2 * std::pow(y3, 2) + A3 * std::pow(y3, 3);
        int x4 = A0 + A1 * y4 + A2 * std::pow(y4, 2) + A3 * std::pow(y4, 3);

        fitting_points.push_back(cv::Point(x1, y1));
        fitting_points.push_back(cv::Point(x2, y2));
        fitting_points.push_back(cv::Point(x3, y3));
        fitting_points.push_back(cv::Point(x4, y4));

        const int n = 1;
        cv::Mat A;
        FitPolynomialCurve(fitting_points, n, A);

        b = A.at<double>(0, 0);
        k = A.at<double>(1, 0);
        */

        std::vector<cv::Point> fitting_points;

        int y0 = MIN(AY, BY);
        int y10 = MAX(AY, BY);

        int dis = y10 - y0;

        int y1 = y0 + dis * 0.1;
        int y2 = y0 + dis * 0.2;
        int y3 = y0 + dis * 0.3;
        int y4 = y0 + dis * 0.4;
        int y5 = y0 + dis * 0.5;
        int y6 = y0 + dis * 0.6;
        int y7 = y0 + dis * 0.7;
        int y8 = y0 + dis * 0.8;
        int y9 = y0 + dis * 0.9;

        int x0 = A0 + A1 * y0 + A2 * std::pow(y0, 2) + A3 * std::pow(y0, 3);
        int x1 = A0 + A1 * y1 + A2 * std::pow(y1, 2) + A3 * std::pow(y1, 3);
        int x2 = A0 + A1 * y2 + A2 * std::pow(y2, 2) + A3 * std::pow(y2, 3);
        int x3 = A0 + A1 * y3 + A2 * std::pow(y3, 2) + A3 * std::pow(y3, 3);
        int x4 = A0 + A1 * y4 + A2 * std::pow(y4, 2) + A3 * std::pow(y4, 3);
        int x5 = A0 + A1 * y5 + A2 * std::pow(y5, 2) + A3 * std::pow(y5, 3);
        int x6 = A0 + A1 * y6 + A2 * std::pow(y6, 2) + A3 * std::pow(y6, 3);
        int x7 = A0 + A1 * y7 + A2 * std::pow(y7, 2) + A3 * std::pow(y7, 3);
        int x8 = A0 + A1 * y8 + A2 * std::pow(y8, 2) + A3 * std::pow(y8, 3);
        int x9 = A0 + A1 * y9 + A2 * std::pow(y9, 2) + A3 * std::pow(y9, 3);
        int x10 = A0 + A1 * y10 + A2 * std::pow(y10, 2) + A3 * std::pow(y10, 3);

        fitting_points.push_back(cv::Point(x0, y0));
        fitting_points.push_back(cv::Point(x1, y1));
        fitting_points.push_back(cv::Point(x2, y2));
        fitting_points.push_back(cv::Point(x3, y3));
        fitting_points.push_back(cv::Point(x4, y4));
        fitting_points.push_back(cv::Point(x5, y5));
        fitting_points.push_back(cv::Point(x6, y6));
        fitting_points.push_back(cv::Point(x7, y7));
        fitting_points.push_back(cv::Point(x8, y8));
        fitting_points.push_back(cv::Point(x9, y9));
        fitting_points.push_back(cv::Point(x10, y10));

        const int n = 1;
        cv::Mat A;
        FitPolynomialCurve(fitting_points, n, A);

        b = A.at<double>(0, 0);
        k = A.at<double>(1, 0);

    }

    void VisDetecter_ADAS_60_Normal::FitPolynomialCurve(const vector<cv::Point> &points, int n, Mat &A) {
         // 最小二乘法多项式曲线拟合原理与实现
        // https://blog.csdn.net/jairuschan/article/details/7517773/
        // https://www.cnblogs.com/fengliu-/p/8031406.html

        int N = points.size();
        cv::Mat X = cv::Mat::zeros(n + 1, n + 1, CV_64FC1);
        for (int i = 0; i < n + 1; i++) {
            for (int j = 0; j < n + 1; j++) {
                for (int k = 0; k < N; k++) {
                    X.at<double>(i, j) = X.at<double>(i, j) +
                                         std::pow(points[k].y, i + j);
                }
            }
        }
        cv::Mat Y = cv::Mat::zeros(n + 1, 1, CV_64FC1);
        for (int i = 0; i < n + 1; i++) {
            for (int k = 0; k < N; k++) {
                Y.at<double>(i, 0) = Y.at<double>(i, 0) +
                                     std::pow(points[k].y, i) * points[k].x;
            }
        }
        A = cv::Mat::zeros(n + 1, 1, CV_64FC1);
        cv::solve(X, Y, A, cv::DECOMP_LU);  //cv::DECOMP_LU
    }
} // vis
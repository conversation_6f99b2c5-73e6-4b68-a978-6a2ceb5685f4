#include <iostream>
#include <cstring>
#include <cstdint>
#include "utils/XuRGAUtils.h"

int main() {
    // Test UYVY422 to NV21 conversion
    const int width = 1280;
    const int height = 720;
    
    // Create test UYVY422 data (2 bytes per pixel)
    const int uyvy_size = width * height * 2;
    uint8_t* uyvy_data = new uint8_t[uyvy_size];
    
    // Fill with test pattern (U=128, Y1=100, V=128, Y2=200)
    for (int i = 0; i < uyvy_size; i += 4) {
        uyvy_data[i] = 128;     // U
        uyvy_data[i + 1] = 100; // Y1
        uyvy_data[i + 2] = 128; // V
        uyvy_data[i + 3] = 200; // Y2
    }
    
    // Create output buffer for NV21 (1.5 bytes per pixel)
    const int nv21_size = width * height * 3 / 2;
    uint8_t* nv21_data = new uint8_t[nv21_size];
    memset(nv21_data, 0, nv21_size);
    
    // Test the conversion
    XuRGAUtils rgaUtils;
    
    std::cout << "Testing UYVY422 to NV21 conversion..." << std::endl;
    std::cout << "Input size: " << uyvy_size << " bytes (" << width << "x" << height << " UYVY422)" << std::endl;
    std::cout << "Expected output size: " << nv21_size << " bytes (" << width << "x" << height << " NV21)" << std::endl;
    
    int result = rgaUtils.convertUYVY422ToNV21(width, height, uyvy_data, width, height, nv21_data);
    
    if (result > 0) {
        std::cout << "✓ Conversion successful! Output size: " << result << " bytes" << std::endl;
        
        // Verify some data
        std::cout << "Verification:" << std::endl;
        std::cout << "  Y plane first few values: ";
        for (int i = 0; i < 10; i++) {
            std::cout << (int)nv21_data[i] << " ";
        }
        std::cout << std::endl;
        
        std::cout << "  UV plane first few values: ";
        int uv_start = width * height;
        for (int i = 0; i < 10; i++) {
            std::cout << (int)nv21_data[uv_start + i] << " ";
        }
        std::cout << std::endl;
        
    } else {
        std::cout << "✗ Conversion failed with error code: " << result << std::endl;
    }
    
    // Test RGA hardware acceleration fallback
    std::cout << "\nTesting RGA hardware acceleration with fallback..." << std::endl;
    memset(nv21_data, 0, nv21_size);
    
    result = rgaUtils.imageTransformation(width, height, XuRGAUtils::IMG_TYPE_UYVY_422, uyvy_data,
                                         width, height, XuRGAUtils::IMG_TYPE_NV21, nv21_data);
    
    if (result > 0) {
        std::cout << "✓ RGA transformation successful! Output size: " << result << " bytes" << std::endl;
    } else {
        std::cout << "✗ RGA transformation failed with error code: " << result << std::endl;
    }
    
    // Cleanup
    delete[] uyvy_data;
    delete[] nv21_data;
    
    return 0;
}

//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/23.
//

#include <sys/time.h>
#include "XuRGAUtils.h"


int XuRGAUtils::synthesisImg_LR(const int leftWidth, const int leftHeight, const uint8_t *leftBuf, const int rightWidth,
                                const int righttHeight, const uint8_t *rightBuf, const int imgFormat) {
    int ret = -1;
    /* 定义RGA需要的图像信息  所有给RGA的图像内容跟需要进行什么操作 都是封装在这个结构体里面再给到RGA的 */
    rga_info_t rgasrc;
    memset(&rgasrc, 0, sizeof(rga_info_t));
    rgasrc.fd = -1;
    rgasrc.mmuFlag = 1;
    rgasrc.virAddr = (char *) leftBuf;
    rga_info_t rgadst;
    memset(&rgadst, 0, sizeof(rga_info_t));
    rgadst.fd = -1;
    rgadst.mmuFlag = 1;
    rgadst.virAddr = (char *) rightBuf;

    /**
     * 这里矩形的设置要注意  贴图的原理是把SRC贴到DST上
     * SRC裁剪出一个区域是需要显示的区域    然后DST裁剪出一个区域   这个区域不是DST需要显示的区域  而是DST准备让SRC贴上去的位置
     *（此点细节文档没有说明，按照裁剪的那一节描述  容易把DST裁剪的区域理解成是需要显示的  此大坑要注意   耗时半天多才试出来）
     * */
    /* 设置好左边图像的图像信息  左边的图就从(0,0)到(leftWidth/2,leftHeight)切矩形或者整张贴上去 因为它在最低层，无所谓 */
    rga_set_rect(&rgasrc.rect, 0, 0, leftWidth / 2, leftHeight, leftWidth/*stride*/, leftHeight, imgFormat);
    /* 设置好右边图像的图像信息  右边的图就从(rightWidth / 2,0)到(rightWidth,leftHeight)切矩形 */
    rga_set_rect(&rgadst.rect, 0, 0, rightWidth / 2, righttHeight, rightWidth/*stride*/, righttHeight,
                 imgFormat);

    /* 设置合成模式为不改变透明度直接贴上去 */
    rgasrc.blend = 0xFF0100;

    /********** 定义两个时针看下合成的耗时 （仅仅测试使用） **********/
//    struct timeval tpend1, tpend2;
//    long usec1 = 0;
//    gettimeofday(&tpend1, nullptr);
    /*调用RGA的接口  把图像信息传进去  让RGA进行对应的操作 */
    ret = rkRga.RkRgaBlit(&rgasrc, &rgadst, nullptr);

//    gettimeofday(&tpend2, nullptr);
//    usec1 = 1000 * (tpend2.tv_sec - tpend1.tv_sec) + (tpend2.tv_usec - tpend1.tv_usec) / 1000;
//    printf("synthesisImg_LR cost_time=%ld ms   ret = %d \n   ", usec1,ret);

    /* 判断下是否成功 */
    if (ret) {
    } else {
        /* 合成完成的图像是在rightBuf中  故不需要在复制一次 */

        /* 计算出输出图像的数据大小 */
        ret = rightWidth * righttHeight * 3;
    }
    return ret;
}

int XuRGAUtils::imageTransformation(const int srcWidth, const int srcHeight, const int srcFormat, uint8_t *const srcBuf,
                                    const int dstWidth, const int dstHeight, const int dstFormat, uint8_t *const dstBuf,
                                    const bool isClip, const int clipStartX, const int clipStartY, const int flipType,
                                    const int rotationType) {
    int ret = -1;
    /* 定义RGA需要的图像信息  所有给RGA的图像内容跟需要进行什么操作 都是封装在这个结构体里面再给到RGA的 */
    rga_info_t rgasrc;
    memset(&rgasrc, 0, sizeof(rga_info_t));
    rgasrc.fd = -1;
    rgasrc.mmuFlag = 1;
    rgasrc.virAddr = srcBuf;

    rga_info_t rgadst;
    memset(&rgadst, 0, sizeof(rga_info_t));
    rgadst.fd = -1;
    rgadst.mmuFlag = 1;
    rgadst.virAddr = dstBuf;


    /* 设置好输入输出图像的图像信息 由于允许输出输入大小不一样 那么就可能是缩放或者裁剪 故这里要根据类型调整输出图像的实际宽高 */
    if (isClip) {
        /* 设置好输入图像的图像信息 */
        rga_set_rect(&rgasrc.rect, clipStartX, clipStartY, srcWidth, srcHeight, dstWidth/*stride*/, dstHeight,
                     srcFormat);
        /* 如果是裁剪的话  那么最后的图像实际宽高就是裁剪出来的区域的大小 */
        rga_set_rect(&rgadst.rect, 0, 0, dstWidth, dstHeight, dstWidth/*stride*/, dstHeight, dstFormat);
    } else {
        /* 设置好输入图像的图像信息 */
        rga_set_rect(&rgasrc.rect, 0, 0, srcWidth, srcHeight, srcWidth/*stride*/, srcHeight, srcFormat);
        /* 如果是缩放的话  那么最后的图像实际宽高就是缩放后大小 */
        rga_set_rect(&rgadst.rect, 0, 0, dstWidth, dstHeight, dstWidth/*stride*/, dstHeight, dstFormat);

    }

    /* 看看是不是需要翻转 */
    if (flipType != -1) {
        rgasrc.rotation = flipType;

    } else {
        /* 如果不需要翻转  那么看看是不是需要缩放 */
        if (rotationType != -1) {
            rgasrc.rotation = rotationType;
        }
    }

    /********** 定义两个时针看下合成的耗时 （仅仅测试使用） **********/
//    struct timeval tpend1, tpend2;
//    long usec1 = 0;
//    gettimeofday(&tpend1, nullptr);
    /*调用RGA的接口  把图像信息传进去  让RGA进行对应的操作 */
    ret = rkRga.RkRgaBlit(&rgasrc, &rgadst, nullptr);

//    gettimeofday(&tpend2, nullptr);
//    usec1 = 1000 * (tpend2.tv_sec - tpend1.tv_sec) + (tpend2.tv_usec - tpend1.tv_usec) / 1000;
//    printf("image transformation  cost_time=%ld ms   ret = %d   dstFormat = %d  \n   ", usec1,ret,dstFormat);

    if (ret != 0) {
        printf("RGA硬件加速失败 (ret=%d)，尝试软件转换...\n", ret);

        // 如果RGA硬件加速失败，尝试软件转换
        if (srcFormat == IMG_TYPE_NV21 && dstFormat == IMG_TYPE_RGBA8888) {
            ret = convertYUV420SPToRGBA8888(srcWidth, srcHeight, srcBuf, dstWidth, dstHeight, dstBuf);
            if (ret > 0) {
                printf("软件YUV420SP->RGBA8888转换成功，输出大小: %d bytes\n", ret);
            } else {
                printf("软件转换也失败了\n");
            }
        } else if (srcFormat == IMG_TYPE_UYVY_422 && dstFormat == IMG_TYPE_NV21) {
            ret = convertUYVY422ToNV21(srcWidth, srcHeight, srcBuf, dstWidth, dstHeight, dstBuf);
            if (ret > 0) {
                printf("软件UYVY422->NV21转换成功，输出大小: %d bytes\n", ret);
            } else {
                printf("软件转换也失败了\n");
            }
        } else {
            printf("不支持的格式转换: %d -> %d\n", srcFormat, dstFormat);
        }
    } else {
        /* RGA处理的时候已经放到输出的内存里面了  所以这里判断下输出的图像格式  计算出数据的大小就行了 */
        switch (dstFormat) {
            case IMG_TYPE_NV21:
            case IMG_TYPE_NV12: {
                ret = dstWidth * dstHeight * 3 / 2;
            }
                break;

            case IMG_TYPE_UYVY_422: {
                ret = dstWidth * dstHeight * 2;
            }
                break;

            case IMG_TYPE_RGB888:
            case IMG_TYPE_BGR888: {
                ret = dstWidth * dstHeight * 3;
            }
                break;

            case IMG_TYPE_RGBA8888:
            case IMG_TYPE_BGRA8888: {
                ret = dstWidth * dstHeight * 4;
            }
                break;
        }
    }
    return ret;
}

int
XuRGAUtils::synthesisImg_Add(const int leftWidth, const int leftHeight, const uint8_t *leftBuf, const int rightWidth,
                             const int rightHeight, const uint8_t *rightBuf) {
    int ret = -1;
    /* 定义RGA需要的图像信息  所有给RGA的图像内容跟需要进行什么操作 都是封装在这个结构体里面再给到RGA的 */
    rga_info_t rgasrc;
    memset(&rgasrc, 0, sizeof(rga_info_t));
    rgasrc.fd = -1;
    rgasrc.mmuFlag = 1;
    rgasrc.virAddr = (char *) leftBuf;
    rga_info_t rgadst;
    memset(&rgadst, 0, sizeof(rga_info_t));
    rgadst.fd = -1;
    rgadst.mmuFlag = 1;
    rgadst.virAddr = (char *) rightBuf;

    /**
     * 这里矩形的设置要注意  贴图的原理是把SRC贴到DST上
     * SRC裁剪出一个区域是需要显示的区域    然后DST裁剪出一个区域   这个区域就不是DST需要显示的区域  而是DST准备让SRC贴上去的位置
     *（此点细节文档没有说明，按照裁剪的那一节描述  容易把DST裁剪的区域理解成是需要显示的  此大坑要注意   耗时半天多才试出来）
     * */
    /* 设置好左边图像的图像信息  左边的图就从(0,0)到(leftWidth/2,leftHeight)切矩形或者整张贴上去 因为它在最低层，无所谓 */
    rga_set_rect(&rgasrc.rect, 0, 0, leftWidth, leftHeight, leftWidth/*stride*/, leftHeight, RK_FORMAT_BGR_888);
    /* 设置好右边图像的图像信息  右边的图就从(rightWidth / 2,0)到(rightWidth,leftHeight)切矩形 */
    rga_set_rect(&rgadst.rect, 0, 0, rightWidth, rightHeight, rightWidth/*stride*/, rightHeight, RK_FORMAT_BGR_888);

    /* 设置合成模式为不改变透明度直接贴上去 */
    rgasrc.blend = 0x4F0105;

    /********** 定义两个时针看下合成的耗时 （仅仅测试使用） **********/
//    struct timeval tpend1, tpend2;
//    long usec1 = 0;
//    gettimeofday(&tpend1, nullptr);
    /*调用RGA的接口  把图像信息传进去  让RGA进行对应的操作 */
    ret = rkRga.RkRgaBlit(&rgasrc, &rgadst, nullptr);
//
//    gettimeofday(&tpend2, nullptr);
//    usec1 = 1000 * (tpend2.tv_sec - tpend1.tv_sec) + (tpend2.tv_usec - tpend1.tv_usec) / 1000;
//    printf("synthesisImg_LR cost_time=%ld ms   ret = %d \n   ", usec1,ret);

    /* 判断下是否成功 */
    if (ret) {
    } else {
        /* 合成完成的图像是在rightBuf中  故不需要在复制一次 */
        /* 计算出输出图像的数据大小 */
        ret = rightWidth * rightHeight * 3;
    }
    return ret;
}

int XuRGAUtils::synthesisImg_R158(const int iconWidth, const int iconHeight, const uint8_t *iconBuf, const int imgWidth,
                                  const int imgHeight, const uint8_t *imgBuf, const int position,const bool needScale) {
    int ret = -1;
    /* 定义RGA需要的图像信息  所有给RGA的图像内容跟需要进行什么操作 都是封装在这个结构体里面再给到RGA的 */
    rga_info_t rgasrc;
    memset(&rgasrc, 0, sizeof(rga_info_t));
    rgasrc.fd = -1;
    rgasrc.mmuFlag = 1;
    rgasrc.virAddr = (char *) iconBuf;
    rga_info_t rgadst;
    memset(&rgadst, 0, sizeof(rga_info_t));
    rgadst.fd = -1;
    rgadst.mmuFlag = 1;
    rgadst.virAddr = (char *) imgBuf;


    /* CVBS显示不全，需要偏移一点图片的位置，让图片显示完全 */
    float verticalDiff = 20;
    float horizontalDiff = 10;

    /* 如果不需要缩放那么就把缩放比设置为1：1，偏移量也设置为0 */
    if(!needScale){
        verticalDiff = 0;
        horizontalDiff = 0;
    }else{
        ; //not to do
    }



    /**
     * 这里矩形的设置要注意  贴图的原理是把SRC贴到DST上
     * SRC裁剪出一个区域是需要显示的区域    然后DST裁剪出一个区域   这个区域不是DST需要显示的区域  而是DST准备让SRC贴上去的位置
     *（此点细节文档没有说明，按照裁剪的那一节描述  容易把DST裁剪的区域理解成是需要显示的  此大坑要注意   耗时半天多才试出来）
     * */
    switch (position) {
        case R158ICON_POSITION_LEFTTOP:{

            /* 设置好ICON的图像，从（0，0），切一块iconWidth*iconHeight的图像出来 */
            rga_set_rect(&rgasrc.rect, 0, 0, iconWidth, iconHeight, iconWidth/*stride*/, iconHeight, RK_FORMAT_RGBA_8888);
            /* 设置好镜头的图像，从（0，0），切一块iconWidth*iconHeight的区域出来放rgasrc的图像 切出来的区域不能超过rgadst图像的分辨率 */
            rga_set_rect(&rgadst.rect, horizontalDiff, verticalDiff, iconWidth, iconHeight, imgWidth/*stride*/, imgHeight,
                         RK_FORMAT_RGBA_8888);
        }
            break;

        case R158ICON_POSITION_RIGHTTOP:{
            /* 设置好ICON的图像形象，从（0，0），切一块iconWidth*iconHeight的图像出来 */
            rga_set_rect(&rgasrc.rect, 0, 0, iconWidth, iconHeight, iconWidth/*stride*/, iconHeight, RK_FORMAT_RGBA_8888);
            /* 设置好镜头的图像，从（imgWidth - iconWidth，0），切一块iconWidth*iconHeight的区域出来放rgasrc的图像 切出来的区域不能超过rgadst图像的分辨率 */
            rga_set_rect(&rgadst.rect, (imgWidth - iconWidth)-horizontalDiff, verticalDiff,iconWidth ,iconHeight , imgWidth/*stride*/, imgHeight,
                         RK_FORMAT_RGBA_8888);
        }
            break;

        case R158ICON_POSITION_CENTRETOP:{

            /* 设置好ICON的图像形象，从（0，0），切一块iconWidth*iconHeight的图像出来 */
            rga_set_rect(&rgasrc.rect, 0, 0, iconWidth, iconHeight, iconWidth/*stride*/, iconHeight, RK_FORMAT_RGBA_8888);
            /* 设置好镜头的图像，从（(imgWidth/2)-(iconWidth/2)，0），切一块iconWidth*iconHeight的区域出来放rgasrc的图像 切出来的区域不能超过rgadst图像的分辨率 */
            rga_set_rect(&rgadst.rect, ((imgWidth/2)-(iconWidth/2)), verticalDiff,iconWidth ,iconHeight , imgWidth/*stride*/, imgHeight,
                         RK_FORMAT_RGBA_8888);
        }
            break;

    }
    /* 设置合成模式为不改变透明度直接贴上去 */
    rgasrc.blend = 0xFF0405;
//    rgasrc.blend = 0xFF0100;

    /********** 定义两个时针看下合成的耗时 （仅仅测试使用） **********/
//    struct timeval tpend1, tpend2;
//    long usec1 = 0;
//    gettimeofday(&tpend1, nullptr);
    /*调用RGA的接口  把图像信息传进去  让RGA进行对应的操作 */
    ret = rkRga.RkRgaBlit(&rgasrc, &rgadst, nullptr);

//    gettimeofday(&tpend2, nullptr);
//    usec1 = 1000 * (tpend2.tv_sec - tpend1.tv_sec) + (tpend2.tv_usec - tpend1.tv_usec) / 1000;
//    printf("synthesisImg_LR cost_time=%ld ms   ret = %d \n   ", usec1,ret);

    /* 判断下是否成功 */
    if (ret) {
    } else {
        /* 合成完成的图像是在rightBuf中  故不需要在复制一次 */

        /* 计算出输出图像的数据大小 */
        ret = imgWidth * imgHeight * 4;
    }
    return ret;
}

int XuRGAUtils::synthesisImg_center(const int srcWidth, const int srcHeight, const int srcFormat, const uint8_t *srcBuf, const int desWidth,
                                const int desHeight, const int desFormat, const uint8_t *desBuf) {

    int ret = -1;
    /* 定义RGA需要的图像信息  所有给RGA的图像内容跟需要进行什么操作 都是封装在这个结构体里面再给到RGA的 */
    rga_info_t rgasrc;
    memset(&rgasrc, 0, sizeof(rga_info_t));
    rgasrc.fd = -1;
    rgasrc.mmuFlag = 1;
    rgasrc.virAddr = (char *) srcBuf;
    rga_info_t rgadst;
    memset(&rgadst, 0, sizeof(rga_info_t));
    rgadst.fd = -1;
    rgadst.mmuFlag = 1;
    rgadst.virAddr = (char *) desBuf;

    /**
     * 这里矩形的设置要注意  贴图的原理是把SRC贴到DST上
     * SRC裁剪出一个区域是需要显示的区域    然后DST裁剪出一个区域   这个区域就不是DST需要显示的区域  而是DST准备让SRC贴上去的位置
     *（此点细节文档没有说明，按照裁剪的那一节描述  容易把DST裁剪的区域理解成是需要显示的  此大坑要注意   耗时半天多才试出来）
     * */
    /* 设置好左边图像的图像信息  左边的图就从(0,0)到(leftWidth/2,leftHeight)切矩形或者整张贴上去 因为它在最低层，无所谓 */
    rga_set_rect(&rgasrc.rect, 0, 0, srcWidth, srcHeight, srcWidth/*stride*/, srcHeight, srcFormat);
    /* 设置好右边图像的图像信息  右边的图就从(rightWidth / 2,0)到(rightWidth,leftHeight)切矩形 */
    rga_set_rect(&rgadst.rect, 0 + ((desWidth - srcWidth)/2), 0 + ((desHeight - srcHeight)/2), srcWidth, srcHeight, desWidth/*stride*/, desHeight, desFormat);

    /* 设置合成模式为不改变透明度直接贴上去 */
    rgasrc.blend = 0xFF0105;

    /********** 定义两个时针看下合成的耗时 （仅仅测试使用） **********/
//    struct timeval tpend1, tpend2;
//    long usec1 = 0;
//    gettimeofday(&tpend1, nullptr);
    /*调用RGA的接口  把图像信息传进去  让RGA进行对应的操作 */
    ret = rkRga.RkRgaBlit(&rgasrc, &rgadst, nullptr);
    if(ret == 0){
        switch (desFormat) {
            case IMG_TYPE_RGB888:
            case IMG_TYPE_BGR888:{
                ret = desWidth * desHeight * 3;

            }
                break;
            case IMG_TYPE_RGBA8888:
            case IMG_TYPE_BGRA8888:{
                ret = desWidth * desHeight * 4;

            }
                break;

            case IMG_TYPE_NV12:
            case IMG_TYPE_NV21:{
                ret = desWidth * desHeight * 3 / 2;

            }
                break;

            case IMG_TYPE_UYVY_422:{
                ret = desWidth * desHeight * 2;
            }
                break;
        }

    }
//    gettimeofday(&tpend2, nullptr);
//    usec1 = 1000 * (tpend2.tv_sec - tpend1.tv_sec) + (tpend2.tv_usec - tpend1.tv_usec) / 1000;
//    printf("synthesisImg_LR cost_time=%ld ms   ret = %d \n   ", usec1,ret);



    return ret;
}

int XuRGAUtils::synthesisImg_FourPalaceGrid(const int leftTopWidth, const int leftTopHeight, const uint8_t *leftTopBuf,
                                            const int rightTopWidth, const int rightTopHeight,
                                            const uint8_t *rightTopBuf, const int leftButtomWidth,
                                            const int leftButtomHeight, const uint8_t *leftButtomBuf,
                                            const int rightButtomWidth, const int rightButtomHeight,
                                            const uint8_t *rightButtomBuf, const int desWidth, const int desHeight,
                                            const uint8_t *desBuf, const int imgFormat) {
    int ret = -1;
    /* 定义RGA需要的图像信息  所有给RGA的图像内容跟需要进行什么操作 都是封装在这个结构体里面再给到RGA的 */
    rga_info_t rgaLeftTop;
    memset(&rgaLeftTop, 0, sizeof(rga_info_t));
    rgaLeftTop.fd = -1;
    rgaLeftTop.mmuFlag = 1;
    rgaLeftTop.virAddr = (char *) leftTopBuf;

    rga_info_t rgaRightTop;
    memset(&rgaRightTop, 0, sizeof(rga_info_t));
    rgaRightTop.fd = -1;
    rgaRightTop.mmuFlag = 1;
    rgaRightTop.virAddr = (char *) rightTopBuf;

    rga_info_t rgaLeftButtom;
    memset(&rgaLeftButtom, 0, sizeof(rga_info_t));
    rgaLeftButtom.fd = -1;
    rgaLeftButtom.mmuFlag = 1;
    rgaLeftButtom.virAddr = (char *) leftButtomBuf;

    rga_info_t rgaRightButtom;
    memset(&rgaRightButtom, 0, sizeof(rga_info_t));
    rgaRightButtom.fd = -1;
    rgaRightButtom.mmuFlag = 1;
    rgaRightButtom.virAddr = (char *) rightButtomBuf;

    rga_info_t rgadst;
    memset(&rgadst, 0, sizeof(rga_info_t));
    rgadst.fd = -1;
    rgadst.mmuFlag = 1;
    rgadst.virAddr = (char *) desBuf;

    /**
     * 这里矩形的设置要注意  贴图的原理是把SRC贴到DST上
     * SRC裁剪出一个区域是需要显示的区域    然后DST裁剪出一个区域   这个区域不是DST需要显示的区域  而是DST准备让SRC贴上去的位置
     *（此点细节文档没有说明，按照裁剪的那一节描述  容易把DST裁剪的区域理解成是需要显示的  此大坑要注意   耗时半天多才试出来）
     * */

    /* 先贴左上的 */

    /* 设置好src图像的图像信息  左边的图就从(0,0)到(leftWidth,leftHeight)整张贴上去 */
    rga_set_rect(&rgaLeftTop.rect, 0, 0, leftTopWidth , leftTopHeight, leftTopWidth/*stride*/, leftTopHeight, imgFormat);
    /* 设置好dst图像的图像信息  右边的图就从(0,desWidth / 2)切宽为desWidth/2，高为desHeight/2的矩形 */
    rga_set_rect(&rgadst.rect, 0, 0, desWidth / 2, desHeight/2, desWidth/*stride*/, desHeight,imgFormat);
    /* 设置合成模式为不改变透明度直接贴上去 */
    rgaLeftTop.blend = 0xFF0100;
    /*调用RGA的接口  把图像信息传进去  让RGA进行对应的操作 */
    ret = rkRga.RkRgaBlit(&rgaLeftTop, &rgadst, nullptr);

    /* 再贴右上的 */

    /* 设置好src图像的图像信息  左边的图就从(0,0)到(leftWidth,leftHeight)整张贴上去 */
    rga_set_rect(&rgaRightTop.rect, 0, 0, rightTopWidth , rightTopHeight, rightTopWidth/*stride*/, rightTopHeight, imgFormat);
    /* 设置好dst图像的图像信息  右边的图就从(desWidth / 2,0)切宽为desWidth/2，高为desHeight/2的矩形 */
    rga_set_rect(&rgadst.rect, desWidth / 2, 0, desWidth/2, desHeight/2, desWidth/*stride*/, desHeight, imgFormat);
    /* 设置合成模式为不改变透明度直接贴上去 */
    rgaRightTop.blend = 0xFF0100;
    /*调用RGA的接口  把图像信息传进去  让RGA进行对应的操作 */
    ret = rkRga.RkRgaBlit(&rgaRightTop, &rgadst, nullptr);


    /* 再贴左下的 */

    /* 设置好src图像的图像信息  左边的图就从(0,0)到(leftWidth,leftHeight)整张贴上去 */
    rga_set_rect(&rgaLeftButtom.rect, 0, 0, leftButtomWidth , leftButtomHeight, leftButtomWidth/*stride*/, leftButtomHeight, imgFormat);
    /* 设置好dst图像的图像信息  右边的图就从(0,desHeight/2)切宽为desWidth/2，高为desHeight/2的矩形 */
    rga_set_rect(&rgadst.rect, 0, desHeight/2, desWidth/2, desHeight/2, desWidth/*stride*/, desHeight, imgFormat);
    /* 设置合成模式为不改变透明度直接贴上去 */
    rgaLeftButtom.blend = 0xFF0100;
    /*调用RGA的接口  把图像信息传进去  让RGA进行对应的操作 */
    ret = rkRga.RkRgaBlit(&rgaLeftButtom, &rgadst, nullptr);


    /* 再贴右下的 */

    /* 设置好src图像的图像信息  左边的图就从(0,0)到(leftWidth,leftHeight)整张贴上去 */
    rga_set_rect(&rgaRightButtom.rect, 0, 0, rightButtomWidth , rightButtomHeight, rightButtomWidth/*stride*/, rightButtomHeight, imgFormat);
    /* 设置好dst图像的图像信息  右边的图就从(0,desHeight/2)切宽为desWidth/2，高为desHeight/2的矩形 */
    rga_set_rect(&rgadst.rect, desWidth/2, desHeight/2, desWidth/2, desHeight/2, desWidth/*stride*/, desHeight, imgFormat);
    /* 设置合成模式为不改变透明度直接贴上去 */
    rgaRightButtom.blend = 0xFF0100;
    /*调用RGA的接口  把图像信息传进去  让RGA进行对应的操作 */
    ret = rkRga.RkRgaBlit(&rgaRightButtom, &rgadst, nullptr);



    /* 判断下是否成功 */
    if (ret) {
    } else {
        /* 合成完成的图像是在rightBuf中  故不需要在复制一次 */

        /* 计算出输出图像的数据大小 */
        ret = desWidth * desHeight * 3;
    }
    return ret;
}

int XuRGAUtils::convertYUV420SPToRGBA8888(int srcWidth, int srcHeight, const uint8_t* srcBuf,
                                          int dstWidth, int dstHeight, uint8_t* dstBuf) {
    if (!srcBuf || !dstBuf) {
        printf("convertYUV420SPToRGBA8888: 无效的缓冲区指针\n");
        return -1;
    }

    if (srcWidth != dstWidth || srcHeight != dstHeight) {
        printf("convertYUV420SPToRGBA8888: 暂不支持缩放，源尺寸和目标尺寸必须相同\n");
        return -1;
    }

    printf("开始软件YUV420SP->RGBA8888转换: %dx%d\n", srcWidth, srcHeight);

    const uint8_t* yPlane = srcBuf;                           // Y平面
    const uint8_t* uvPlane = srcBuf + srcWidth * srcHeight;   // UV平面 (NV21格式)

    // YUV420SP到RGBA8888的转换
    for (int y = 0; y < srcHeight; y++) {
        for (int x = 0; x < srcWidth; x++) {
            // 获取Y值
            int yIndex = y * srcWidth + x;
            int Y = yPlane[yIndex];

            // 获取U和V值 (NV21格式: VUVUVU...)
            int uvIndex = (y / 2) * srcWidth + (x / 2) * 2;
            int V = uvPlane[uvIndex];     // V在前
            int U = uvPlane[uvIndex + 1]; // U在后

            // YUV到RGB转换公式
            int R = Y + 1.402 * (V - 128);
            int G = Y - 0.344 * (U - 128) - 0.714 * (V - 128);
            int B = Y + 1.772 * (U - 128);

            // 限制RGB值在0-255范围内
            R = (R < 0) ? 0 : (R > 255) ? 255 : R;
            G = (G < 0) ? 0 : (G > 255) ? 255 : G;
            B = (B < 0) ? 0 : (B > 255) ? 255 : B;

            // 写入RGBA8888格式 (R, G, B, A)
            int rgbaIndex = (y * dstWidth + x) * 4;
            dstBuf[rgbaIndex + 0] = (uint8_t)R;     // R
            dstBuf[rgbaIndex + 1] = (uint8_t)G;     // G
            dstBuf[rgbaIndex + 2] = (uint8_t)B;     // B
            dstBuf[rgbaIndex + 3] = 255;            // A (不透明)
        }
    }

    int outputSize = dstWidth * dstHeight * 4;
    printf("软件转换完成，输出大小: %d bytes\n", outputSize);
    return outputSize;
}

int XuRGAUtils::convertUYVY422ToNV21(int srcWidth, int srcHeight, const uint8_t* srcBuf,
                                      int dstWidth, int dstHeight, uint8_t* dstBuf) {
    if (!srcBuf || !dstBuf) {
        printf("convertUYVY422ToNV21: 无效的缓冲区指针\n");
        return -1;
    }

    if (srcWidth != dstWidth || srcHeight != dstHeight) {
        printf("convertUYVY422ToNV21: 暂不支持缩放，源尺寸和目标尺寸必须相同\n");
        return -1;
    }

    printf("开始软件UYVY422->NV21转换: %dx%d\n", srcWidth, srcHeight);

    // UYVY422格式：每2个像素占用4个字节 (U Y V Y)
    // NV21格式：Y平面 + UV交错平面 (YUV420SP)

    const uint8_t* src = srcBuf;
    uint8_t* yPlane = dstBuf;                           // Y平面
    uint8_t* uvPlane = dstBuf + srcWidth * srcHeight;   // UV平面

    // 转换Y分量
    for (int y = 0; y < srcHeight; y++) {
        for (int x = 0; x < srcWidth; x += 2) {
            int srcIndex = (y * srcWidth + x) * 2;  // UYVY每2个像素4字节
            int yIndex1 = y * srcWidth + x;
            int yIndex2 = y * srcWidth + x + 1;

            // UYVY格式: U Y V Y
            yPlane[yIndex1] = src[srcIndex + 1];     // 第一个Y
            yPlane[yIndex2] = src[srcIndex + 3];     // 第二个Y
        }
    }

    // 转换UV分量 (4:2:0子采样，每2x2像素块共享一组UV)
    for (int y = 0; y < srcHeight; y += 2) {
        for (int x = 0; x < srcWidth; x += 2) {
            int srcIndex = (y * srcWidth + x) * 2;  // UYVY每2个像素4字节
            int uvIndex = (y / 2) * srcWidth + x;   // UV平面索引

            // UYVY格式: U Y V Y
            uint8_t u = src[srcIndex];               // U分量
            uint8_t v = src[srcIndex + 2];           // V分量

            // NV21格式是VU交错存储
            uvPlane[uvIndex] = v;                    // V在前
            uvPlane[uvIndex + 1] = u;                // U在后
        }
    }

    int outputSize = srcWidth * srcHeight * 3 / 2;  // YUV420格式大小
    printf("软件UYVY422->NV21转换完成，输出大小: %d bytes\n", outputSize);
    return outputSize;
}

